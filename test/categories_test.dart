import 'package:flutter_test/flutter_test.dart';
import 'package:pocket_trac/app/utils/category_icons.dart';
import 'package:pocket_trac/app/models/erp_category.dart';
import 'package:flutter/material.dart';

void main() {
  group('CategoryIcons Tests', () {
    test('should return correct icon for known category', () {
      expect(CategoryIcons.getIcon('薪水'), Icons.attach_money);
      expect(CategoryIcons.getIcon('餐飲'), Icons.restaurant);
      expect(CategoryIcons.getIcon('購物'), Icons.shopping_bag);
    });

    test('should return default icon for unknown category', () {
      expect(CategoryIcons.getIcon('未知分類'), Icons.category);
      expect(CategoryIcons.getIcon(null), Icons.category);
      expect(CategoryIcons.getIcon(''), Icons.category);
    });

    test('should return correct color for known category', () {
      expect(CategoryIcons.getColor('薪水'), isA<Color>());
      expect(CategoryIcons.getColor('餐飲'), isA<Color>());
    });

    test('should return correct background color for known category', () {
      expect(CategoryIcons.getBackgroundColor('薪水'), isA<Color>());
      expect(CategoryIcons.getBackgroundColor('餐飲'), isA<Color>());
    });

    test('should parse color string correctly', () {
      expect(CategoryIcons.parseColor('#FF0000'), isA<Color>());
      expect(CategoryIcons.parseColor('薪水'), isA<Color>());
      expect(CategoryIcons.parseColor(null), isA<Color>());
    });

    test('should return all category names', () {
      final names = CategoryIcons.getAllCategoryNames();
      expect(names, isNotEmpty);
      expect(names, contains('薪水'));
      expect(names, contains('餐飲'));
    });
  });

  group('ErpCategory Tests', () {
    test('should create category with correct properties', () {
      final category = ErpCategory(
        name: '測試分類',
        color: '#FF0000',
      );

      expect(category.name, '測試分類');
      expect(category.color, '#FF0000');
    });

    test('should convert to and from JSON correctly', () {
      final category = ErpCategory(
        name: '測試分類',
        color: '#FF0000',
      );

      final json = category.toJson();
      final fromJson = ErpCategory.fromJson(json);

      expect(fromJson.name, category.name);
      expect(fromJson.color, category.color);
    });

    test('should copy with new values', () {
      final original = ErpCategory(
        name: '原始分類',
        color: '#FF0000',
      );

      final copied = original.copyWith(
        name: '新分類',
        color: '#00FF00',
      );

      expect(copied.name, '新分類');
      expect(copied.color, '#00FF00');
      expect(copied.id, original.id); // Should keep original value
    });
  });
}
