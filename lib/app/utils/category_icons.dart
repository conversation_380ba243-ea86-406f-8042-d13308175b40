import 'package:flutter/material.dart';
import '../../colors.dart';

class CategoryIcons {
  // 分类图标映射（基于原型设计）
  static const Map<String, IconData> iconMap = {
    '薪水': Icons.attach_money,
    '餐飲': Icons.restaurant,
    '購物': Icons.shopping_bag,
    '交通': Icons.directions_car,
    '娛樂': Icons.sports_esports,
    '獎金': Icons.card_giftcard,
    '居家': Icons.home,
    '副業': Icons.work,
    '健康': Icons.local_hospital,
    '教育': Icons.school,
    '水電': Icons.electrical_services,
    '其他': Icons.category,
  };

  // 分类颜色映射（基于原型设计）
  static const Map<String, Color> colorMap = {
    '薪水': ErpColors.success,
    '餐飲': ErpColors.error,
    '購物': ErpColors.accent,
    '交通': ErpColors.primary,
    '娛樂': ErpColors.categoryEntertainment,
    '獎金': ErpColors.warning,
    '居家': ErpColors.categoryUtilities,
    '副業': ErpColors.accentDark,
    '健康': ErpColors.categoryHealth,
    '教育': ErpColors.categoryEducation,
    '水電': ErpColors.textSecondary,
    '其他': ErpColors.categoryOther,
  };

  // 分类背景颜色映射（浅色版本）
  static const Map<String, Color> backgroundColorMap = {
    '薪水': ErpColors.iconBackgroundGreen,
    '餐飲': ErpColors.iconBackgroundRed,
    '購物': ErpColors.iconBackgroundPurple,
    '交通': ErpColors.iconBackgroundBlue,
    '娛樂': ErpColors.iconBackgroundYellow,
    '獎金': ErpColors.iconBackgroundYellow,
    '居家': ErpColors.iconBackgroundYellow,
    '副業': ErpColors.iconBackgroundPurple,
    '健康': ErpColors.iconBackgroundBlue,
    '教育': ErpColors.iconBackgroundPurple,
    '水電': ErpColors.iconBackgroundYellow,
    '其他': ErpColors.iconBackgroundGreen,
  };

  /// 根据分类名称获取图标
  static IconData getIcon(String? categoryName) {
    if (categoryName == null || categoryName.isEmpty) {
      return iconMap['其他'] ?? Icons.category;
    }
    return iconMap[categoryName] ?? Icons.category;
  }

  /// 根据分类名称获取颜色
  static Color getColor(String? categoryName) {
    if (categoryName == null || categoryName.isEmpty) {
      return colorMap['其他'] ?? ErpColors.categoryOther;
    }
    return colorMap[categoryName] ?? ErpColors.categoryOther;
  }

  /// 根据分类名称获取背景颜色
  static Color getBackgroundColor(String? categoryName) {
    if (categoryName == null || categoryName.isEmpty) {
      return backgroundColorMap['其他'] ?? ErpColors.iconBackgroundGreen;
    }
    return backgroundColorMap[categoryName] ?? ErpColors.iconBackgroundGreen;
  }

  /// 获取所有可用的分类图标
  static List<String> getAllCategoryNames() {
    return iconMap.keys.toList();
  }

  /// 根据颜色字符串获取颜色对象
  static Color parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) {
      return ErpColors.categoryOther;
    }
    
    // 如果是十六进制颜色字符串
    if (colorString.startsWith('#')) {
      try {
        return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      } catch (e) {
        return ErpColors.categoryOther;
      }
    }
    
    // 如果是预定义的颜色名称
    return getColor(colorString);
  }
}
