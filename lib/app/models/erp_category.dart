import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:objectbox/objectbox.dart';

import '../../colors.dart';
import 'erp_order.dart';

@Entity()
class ErpCategory {
  @Id()
  int? id;
  @Property(type: PropertyType.date) // Store as int in milliseconds
  DateTime? createdAt;
  @Property(type: PropertyType.date) // Store as int in milliseconds
  DateTime? updatedAt;
  @Property(type: PropertyType.date) // Store as int in milliseconds
  DateTime? deletedAt;
  String? icon;
  String? name;
  String? color;
  String? objectId;
  @Backlink()
  final children = ToMany<ErpOrder>();
  // Not persisted:
  @Transient()
  num? amount;

  ErpCategory({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.icon,
    this.name,
    this.objectId,
    this.color,
  });

  ErpCategory copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    String? name,
    String? objectId,
    String? icon,
    String? color,
  }) =>
      ErpCategory(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        icon: icon ?? this.icon,
        name: name ?? this.name,
        objectId: objectId ?? this.objectId,
        color: color ?? this.color,
      );

  factory ErpCategory.fromRawJson(String str) =>
      ErpCategory.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ErpCategory.fromJson(Map<String, dynamic> json) => ErpCategory(
        id: json["id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"] == null
            ? null
            : DateTime.parse(json["deleted_at"]),
        icon: json["icon"],
        name: json["name"],
        objectId: json["object_id"],
        color: json["color"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt?.toIso8601String(),
        "icon": icon,
        "name": name,
        "object_id": objectId,
        "color": color,
      };

  // Helper method to get the color as a Color object
  Color getColor() {
    if (color == null || color!.isEmpty) {
      return ErpColors.primary; // Default color
    }

    try {
      return Color(int.parse(color!.replaceFirst('#', '0xFF')));
    } catch (e) {
      // If parsing fails, return default color
      return ErpColors.primary;
    }
  }
}
